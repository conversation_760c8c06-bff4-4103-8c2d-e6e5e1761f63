/* Modern OMS CSS Variables and Base Styles */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --light-bg: #f8fafc;
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--light-bg);
  color: var(--gray-800);
  line-height: 1.6;
  font-size: 14px;
  overflow-x: hidden;
}

/* Modern Navigation */
.modern-nav {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 1rem 2rem;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--white);
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
}

.nav-brand i {
  font-size: 2rem;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-search {
  position: relative;
  display: flex;
  align-items: center;
}

.nav-search input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  color: var(--white);
  width: 300px;
  transition: var(--transition);
}

.nav-search input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.nav-search input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.nav-search i {
  position: absolute;
  left: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--white);
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--primary-color);
}

/* Modern Top Navigation Tabs */
.modern-nav-tabs {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 80px;
  z-index: 998;
}

.nav-tabs-container {
  display: flex;
  align-items: center;
  gap: 0;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-tabs-container::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  color: var(--gray-600);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
}

.nav-tab:hover {
  color: var(--primary-color);
  background: var(--gray-50);
}

.nav-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: linear-gradient(to bottom, rgba(37, 99, 235, 0.05), transparent);
}

.nav-tab i {
  font-size: 1rem;
}

.nav-badge {
  background: var(--primary-color);
  color: var(--white);
  font-size: 0.65rem;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  font-weight: 600;
}

.nav-tab-group {
  display: flex;
  align-items: center;
  position: relative;
  margin: 0 1rem;
}

.nav-tab-group-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-400);
  margin-right: 1rem;
  padding: 1rem 0;
}

.nav-tab-items {
  display: flex;
  align-items: center;
  gap: 0;
}

.nav-tab-group::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background: var(--gray-200);
}

.nav-tab-group::after {
  content: '';
  position: absolute;
  right: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background: var(--gray-200);
}

/* Main Content Area - Modern Full Width */
.main-content-modern {
  padding: 2rem 2rem 120px 2rem;
  min-height: calc(100vh - 140px);
  max-width: 1400px;
  margin: 0 auto;
}

/* Modern Content Sections */
.content-section {
  display: none;
  animation: fadeInUp 0.4s ease-out;
}

.content-section.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-100);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
  margin-bottom: 1rem;
}

.stat-icon.primary { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
.stat-icon.success { background: linear-gradient(135deg, var(--success-color), #34d399); }
.stat-icon.warning { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }
.stat-icon.info { background: linear-gradient(135deg, var(--info-color), #22d3ee); }

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0.5rem;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

/* Modern Tables */
.modern-table-container {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-100);
  overflow: hidden;
  margin-bottom: 2rem;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
}

.table-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-table th {
  background: var(--gray-50);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-800);
  font-size: 0.875rem;
}

.modern-table tbody tr {
  transition: var(--transition);
}

.modern-table tbody tr:hover {
  background: var(--gray-50);
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.new {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.received {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.open {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.closed {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-600);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-badge.rejected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #34d399);
  color: var(--white);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
  color: var(--white);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #f87171);
  color: var(--white);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Modern Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  transition: var(--transition);
  background: var(--white);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:disabled {
  background: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Search and Filters */
.search-filters {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-100);
  margin-bottom: 2rem;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.search-input {
  position: relative;
}

.search-input input {
  padding-left: 2.5rem;
}

.search-input i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--gray-300);
  background: var(--white);
  color: var(--gray-700);
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: var(--transition);
  font-size: 0.875rem;
}

.pagination-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.modal {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-400);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Chart Container */
.chart-container {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-100);
  margin-bottom: 2rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
}

.chart-canvas {
  width: 100%;
  height: 300px;
}

/* Activity Feed */
.activity-feed {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-100);
}

.activity-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--gray-100);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.activity-icon.order { background: rgba(59, 130, 246, 0.1); color: var(--primary-color); }
.activity-icon.customer { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
.activity-icon.payment { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: 0.875rem;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--gray-500);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--gray-500); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content-modern {
    padding: 1.5rem;
  }

  .nav-tabs-container {
    padding: 0 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .nav-tab-group-title {
    display: none;
  }

  .nav-tab-group::before,
  .nav-tab-group::after {
    display: none;
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-search input {
    width: 200px;
  }

  .user-profile span {
    display: none;
  }

  .main-content-modern {
    padding: 1rem;
  }

  .nav-tabs-container {
    padding: 0 0.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .nav-tab span {
    display: none;
  }

  .nav-tab i {
    font-size: 1.1rem;
  }

  .nav-tab-group {
    margin: 0 0.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .table-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .modern-table-container {
    overflow-x: auto;
  }

  .modern-table {
    min-width: 600px;
  }

  .modal {
    width: 95%;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .nav-search {
    display: none;
  }

  .main-content-modern {
    padding: 1rem 0.5rem 100px 0.5rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .table-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination {
    gap: 0.25rem;
  }

  .pagination-btn {
    width: 35px;
    height: 35px;
    font-size: 0.75rem;
  }

  .fab-container {
    bottom: 120px;
    right: 1rem;
  }

  .fab-main {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .fab-item {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .quick-stats-bar {
    padding: 0.75rem 1rem;
    gap: 1.5rem;
  }

  .quick-stat-value {
    font-size: 1rem;
  }

  .quick-stat-label {
    font-size: 0.65rem;
  }
}

/* Loading States */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Modern Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.fab-main {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  color: var(--white);
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.fab-main.active {
  transform: rotate(45deg);
}

.fab-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: var(--transition);
}

.fab-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-item {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--white);
  border: 1px solid var(--gray-200);
  color: var(--gray-700);
  font-size: 1.25rem;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-item:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: scale(1.1);
}

/* Modern Quick Stats Bar */
.quick-stats-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: 1rem 2rem;
  display: flex;
  justify-content: center;
  gap: 3rem;
  box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.1);
  z-index: 999;
}

.quick-stat {
  text-align: center;
  min-width: 80px;
}

.quick-stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.quick-stat-label {
  font-size: 0.75rem;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modern Card Hover Effects */
.stat-card {
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

/* Modern Table Row Hover */
.modern-table tbody tr:hover {
  background: linear-gradient(90deg, rgba(37, 99, 235, 0.02), rgba(37, 99, 235, 0.05));
  transform: translateX(2px);
}

/* Modern Button Enhancements */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

/* Print Styles */
@media print {
  .modern-nav,
  .modern-nav-tabs,
  .fab-container,
  .quick-stats-bar,
  .btn,
  .pagination {
    display: none !important;
  }

  .main-content-modern {
    padding: 0;
  }

  .stat-card,
  .modern-table-container {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
}