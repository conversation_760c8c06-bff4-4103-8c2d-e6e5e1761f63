<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIS Order Management System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="Oms.css">
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="modern-nav">
        <div class="nav-container">
            <a href="#" class="nav-brand">
                <i class="fas fa-cube"></i>
                <span>AIS OMS</span>
            </a>

            <div class="nav-actions">
                <div class="nav-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search orders, customers..." id="globalSearch">
                </div>

                <div class="user-profile" onclick="toggleUserMenu()">
                    <div class="user-avatar">A</div>
                    <span>Admin User</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- Modern Sidebar -->
    <aside class="modern-sidebar" id="sidebar">
        <div class="sidebar-section">
            <div class="sidebar-title">Main</div>
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link active" data-section="dashboard">
                        <i class="fas fa-chart-pie"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-section">
            <div class="sidebar-title">Orders</div>
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="new-orders">
                        <i class="fas fa-plus-circle"></i>
                        <span>New Orders</span>
                        <span class="sidebar-badge" id="newOrdersCount">23</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="received-orders">
                        <i class="fas fa-inbox"></i>
                        <span>Received Orders</span>
                        <span class="sidebar-badge" id="receivedOrdersCount">45</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="open-orders">
                        <i class="fas fa-folder-open"></i>
                        <span>Open Orders</span>
                        <span class="sidebar-badge" id="openOrdersCount">67</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="closed-orders">
                        <i class="fas fa-check-circle"></i>
                        <span>Closed Orders</span>
                        <span class="sidebar-badge" id="closedOrdersCount">21</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="rejected-orders">
                        <i class="fas fa-times-circle"></i>
                        <span>Rejected Orders</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="cancelled-orders">
                        <i class="fas fa-ban"></i>
                        <span>Cancelled Orders</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="failed-orders">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Failed Orders</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-section">
            <div class="sidebar-title">Management</div>
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="customers">
                        <i class="fas fa-users"></i>
                        <span>Customers</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="stocks">
                        <i class="fas fa-boxes"></i>
                        <span>Stocks</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="outstanding-aging">
                        <i class="fas fa-clock"></i>
                        <span>Outstanding Aging</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="add-on-schema">
                        <i class="fas fa-puzzle-piece"></i>
                        <span>Add On Schema</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-section">
            <div class="sidebar-title">Services</div>
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="offers">
                        <i class="fas fa-tags"></i>
                        <span>Offers</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section active">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">Dashboard</h1>
                    <p class="text-muted">Welcome back! Here's what's happening with your orders today.</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-secondary btn-sm">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        New Order
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon primary">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-value" id="totalOrdersValue">156</div>
                    <div class="stat-label">Total Orders</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>12.5% from last month</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value" id="completedOrdersValue">89</div>
                    <div class="stat-label">Completed Orders</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>8.2% from last month</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value" id="pendingOrdersValue">67</div>
                    <div class="stat-label">Pending Orders</div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>3.1% from last month</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-value" id="revenueValue">₹24.5L</div>
                    <div class="stat-label">Total Revenue</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>15.3% from last month</span>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">Order Trends</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary active">7D</button>
                                <button class="btn btn-outline-primary">30D</button>
                                <button class="btn btn-outline-primary">90D</button>
                            </div>
                        </div>
                        <canvas id="orderTrendsChart" class="chart-canvas"></canvas>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">Order Status Distribution</h5>
                        </div>
                        <canvas id="orderStatusChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="modern-table-container">
                        <div class="table-header">
                            <h5 class="table-title">Recent Orders</h5>
                            <div class="table-actions">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-filter"></i>
                                    Filter
                                </button>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-download"></i>
                                    Export
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="modern-table">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Status</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recentOrdersTable">
                                    <!-- Dynamic content will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="activity-feed">
                        <h5 class="mb-3">Recent Activity</h5>
                        <div id="activityFeed">
                            <!-- Dynamic content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Orders Section -->
        <div id="new-orders-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">New Orders</h1>
                    <p class="text-muted">Manage and process new incoming orders</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="showNewOrderModal()">
                        <i class="fas fa-plus"></i>
                        Create Order
                    </button>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="search-input">
                        <i class="fas fa-search"></i>
                        <input type="text" class="form-control" placeholder="Search by order ID, customer name..." id="newOrdersSearch">
                    </div>
                    <div>
                        <select class="form-select" id="territoryFilter">
                            <option value="">All Territories</option>
                            <option value="North">North</option>
                            <option value="South">South</option>
                            <option value="East">East</option>
                            <option value="West">West</option>
                        </select>
                    </div>
                    <div>
                        <select class="form-select" id="stateFilter">
                            <option value="">All States</option>
                            <option value="Maharashtra">Maharashtra</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Karnataka">Karnataka</option>
                            <option value="Gujarat">Gujarat</option>
                        </select>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="modern-table-container">
                <div class="table-header">
                    <h5 class="table-title">New Orders List</h5>
                    <div class="table-actions">
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-check"></i>
                            Bulk Approve
                        </button>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th>GST #</th>
                                <th>Ship To PAN</th>
                                <th>Email ID</th>
                                <th>Bill To GST</th>
                                <th>Customer Name</th>
                                <th>Operating Unit</th>
                                <th>Contact Person</th>
                                <th>Contact #</th>
                                <th>Territory</th>
                                <th>State</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="newOrdersTable">
                            <!-- Dynamic content will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button class="pagination-btn" id="prevPage">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="pageNumbers"></span>
                    <button class="pagination-btn" id="nextPage">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Received Orders Section -->
        <div id="received-orders-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">Received Orders</h1>
                    <p class="text-muted">Orders that have been received and are being processed</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="modern-table-container">
                <div class="table-header">
                    <h5 class="table-title">Received Orders List</h5>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Oracle Order #</th>
                                <th>Created Date</th>
                                <th>Status</th>
                                <th>Zone</th>
                                <th>Customer Name</th>
                                <th>Operating Unit</th>
                                <th>Contact Person</th>
                                <th>Contact #</th>
                                <th>Bill To Address</th>
                                <th>State</th>
                                <th>Territory</th>
                                <th>Email ID</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="receivedOrdersTable">
                            <!-- Dynamic content will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Other Order Sections (Open, Closed, etc.) -->
        <div id="open-orders-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">Open Orders</h1>
                    <p class="text-muted">Orders currently being processed</p>
                </div>
            </div>
            <div class="modern-table-container">
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer Name</th>
                                <th>Status</th>
                                <th>Order Value</th>
                                <th>Created Date</th>
                                <th>Territory</th>
                                <th>State</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="openOrdersTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="closed-orders-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">Closed Orders</h1>
                    <p class="text-muted">Successfully completed orders</p>
                </div>
            </div>
            <div class="modern-table-container">
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer Name</th>
                                <th>Status</th>
                                <th>Order Value</th>
                                <th>Created Date</th>
                                <th>Closed Date</th>
                                <th>Territory</th>
                                <th>State</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="closedOrdersTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customers-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 font-bold text-gray-900 mb-1">Customers</h1>
                    <p class="text-muted">Manage your customer database</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        Add Customer
                    </button>
                </div>
            </div>
            <div class="modern-table-container">
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Customer ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Address</th>
                                <th>GST Number</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customersTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Other sections placeholders -->
        <div id="stocks-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Stocks</h1>
            <p class="text-muted">Stock management functionality coming soon...</p>
        </div>

        <div id="outstanding-aging-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Outstanding Aging</h1>
            <p class="text-muted">Outstanding aging reports coming soon...</p>
        </div>

        <div id="add-on-schema-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Add On Schema</h1>
            <p class="text-muted">Add-on schema management coming soon...</p>
        </div>

        <div id="offers-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Offers</h1>
            <p class="text-muted">Offers management coming soon...</p>
        </div>

        <div id="reports-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Reports</h1>
            <p class="text-muted">Advanced reporting functionality coming soon...</p>
        </div>

        <div id="settings-section" class="content-section">
            <h1 class="h3 font-bold text-gray-900 mb-3">Settings</h1>
            <p class="text-muted">System settings coming soon...</p>
        </div>
    </main>

    <!-- New Order Modal -->
    <div class="modal-overlay" id="newOrderModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h5 class="modal-title">Create New Order</h5>
                <button class="modal-close" onclick="hideNewOrderModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newOrderForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Customer Name</label>
                                <input type="text" class="form-control" name="customerName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">GST Number</label>
                                <input type="text" class="form-control" name="gstNumber" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Contact Person</label>
                                <input type="text" class="form-control" name="contactPerson" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Contact Number</label>
                                <input type="tel" class="form-control" name="contactNumber" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Email ID</label>
                                <input type="email" class="form-control" name="emailId" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Operating Unit</label>
                                <select class="form-select" name="operatingUnit" required>
                                    <option value="">Select Unit</option>
                                    <option value="Unit A">Unit A</option>
                                    <option value="Unit B">Unit B</option>
                                    <option value="Unit C">Unit C</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ship To Address</label>
                        <textarea class="form-control" name="shipToAddress" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Territory</label>
                                <select class="form-select" name="territory" required>
                                    <option value="">Select Territory</option>
                                    <option value="North">North</option>
                                    <option value="South">South</option>
                                    <option value="East">East</option>
                                    <option value="West">West</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">State</label>
                                <select class="form-select" name="state" required>
                                    <option value="">Select State</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Delhi">Delhi</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Gujarat">Gujarat</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideNewOrderModal()">Cancel</button>
                <button class="btn btn-primary" onclick="createNewOrder()">Create Order</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="Oms.js"></script>
</body>
</html>