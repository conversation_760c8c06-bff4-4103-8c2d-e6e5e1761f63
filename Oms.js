// Modern OMS JavaScript
class OrderManagementSystem {
    constructor() {
        this.data = null;
        this.currentSection = 'dashboard';
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.charts = {};

        this.init();
    }

    async init() {
        try {
            this.loadData();
            this.setupEventListeners();
            this.initializeCharts();
            this.renderDashboard();
            this.showSection('dashboard');
        } catch (error) {
            console.error('Failed to initialize OMS:', error);
            this.showError('Failed to load application data');
        }
    }

    loadData() {
        // Embedded JSON data to avoid CORS issues
        this.data = {
            "orders": {
                "newOrders": [
                    {
                        "id": "ORD001",
                        "gstNumber": "GST123456789",
                        "shipToPan": "PAN001",
                        "emailId": "<EMAIL>",
                        "billToGst": "GST987654321",
                        "billToPan": "PAN002",
                        "customerName": "ABC Corporation",
                        "operatingUnit": "Unit A",
                        "contactPerson": "<PERSON>",
                        "contactNumber": "+91-9876543210",
                        "shipToAddress": "123 Business Park, Mumbai",
                        "territory": "West",
                        "state": "Maharashtra",
                        "status": "New",
                        "createdDate": "2024-01-15",
                        "orderValue": 125000
                    },
                    {
                        "id": "ORD002",
                        "gstNumber": "GST456789123",
                        "shipToPan": "PAN003",
                        "emailId": "<EMAIL>",
                        "billToGst": "GST321654987",
                        "billToPan": "PAN004",
                        "customerName": "XYZ Industries",
                        "operatingUnit": "Unit B",
                        "contactPerson": "Jane Smith",
                        "contactNumber": "+91-8765432109",
                        "shipToAddress": "456 Industrial Area, Delhi",
                        "territory": "North",
                        "state": "Delhi",
                        "status": "New",
                        "createdDate": "2024-01-16",
                        "orderValue": 89000
                    },
                    {
                        "id": "ORD006",
                        "gstNumber": "GST789123456",
                        "shipToPan": "PAN005",
                        "emailId": "<EMAIL>",
                        "billToGst": "GST654321987",
                        "billToPan": "PAN006",
                        "customerName": "Tech Solutions Pvt Ltd",
                        "operatingUnit": "Unit C",
                        "contactPerson": "Raj Kumar",
                        "contactNumber": "+91-7654321098",
                        "shipToAddress": "789 Tech Park, Bangalore",
                        "territory": "South",
                        "state": "Karnataka",
                        "status": "New",
                        "createdDate": "2024-01-17",
                        "orderValue": 156000
                    }
                ],
                "receivedOrders": [
                    {
                        "id": "ORD003",
                        "orderNumber": "RO001",
                        "oracleOrderNumber": "ORA001",
                        "createdDate": "2024-01-10",
                        "status": "Received",
                        "zone": "Zone A",
                        "customerName": "Tech Solutions Ltd",
                        "operatingUnit": "Unit C",
                        "contactPerson": "Mike Johnson",
                        "contactNumber": "+91-7654321098",
                        "billToAddress": "789 Tech Hub, Bangalore",
                        "state": "Karnataka",
                        "territory": "South",
                        "emailId": "<EMAIL>",
                        "orderValue": 156000
                    },
                    {
                        "id": "ORD007",
                        "orderNumber": "RO002",
                        "oracleOrderNumber": "ORA002",
                        "createdDate": "2024-01-12",
                        "status": "Received",
                        "zone": "Zone B",
                        "customerName": "Global Enterprises",
                        "operatingUnit": "Unit A",
                        "contactPerson": "Sarah Wilson",
                        "contactNumber": "+91-9876543210",
                        "billToAddress": "456 Business Center, Mumbai",
                        "state": "Maharashtra",
                        "territory": "West",
                        "emailId": "<EMAIL>",
                        "orderValue": 234000
                    }
                ],
                "openOrders": [
                    {
                        "id": "ORD004",
                        "orderNumber": "OP001",
                        "customerName": "Global Enterprises",
                        "status": "Open",
                        "orderValue": 234000,
                        "createdDate": "2024-01-12",
                        "territory": "East",
                        "state": "West Bengal"
                    },
                    {
                        "id": "ORD008",
                        "orderNumber": "OP002",
                        "customerName": "Prime Industries",
                        "status": "Open",
                        "orderValue": 178000,
                        "createdDate": "2024-01-14",
                        "territory": "North",
                        "state": "Punjab"
                    }
                ],
                "closedOrders": [
                    {
                        "id": "ORD005",
                        "orderNumber": "CL001",
                        "customerName": "Prime Industries",
                        "status": "Closed",
                        "orderValue": 178000,
                        "createdDate": "2024-01-08",
                        "closedDate": "2024-01-14",
                        "territory": "West",
                        "state": "Gujarat"
                    }
                ]
            },
            "customers": [
                {
                    "id": "CUST001",
                    "name": "ABC Corporation",
                    "email": "<EMAIL>",
                    "phone": "+91-9876543210",
                    "address": "123 Business Park, Mumbai",
                    "gstNumber": "GST123456789",
                    "status": "Active"
                },
                {
                    "id": "CUST002",
                    "name": "XYZ Industries",
                    "email": "<EMAIL>",
                    "phone": "+91-8765432109",
                    "address": "456 Industrial Area, Delhi",
                    "gstNumber": "GST456789123",
                    "status": "Active"
                },
                {
                    "id": "CUST003",
                    "name": "Tech Solutions Pvt Ltd",
                    "email": "<EMAIL>",
                    "phone": "+91-7654321098",
                    "address": "789 Tech Park, Bangalore",
                    "gstNumber": "GST789123456",
                    "status": "Active"
                },
                {
                    "id": "CUST004",
                    "name": "Global Enterprises",
                    "email": "<EMAIL>",
                    "phone": "+91-9876543210",
                    "address": "456 Business Center, Mumbai",
                    "gstNumber": "GST654321987",
                    "status": "Active"
                }
            ],
            "dashboard": {
                "stats": {
                    "totalOrders": 156,
                    "newOrders": 23,
                    "receivedOrders": 45,
                    "openOrders": 67,
                    "closedOrders": 21,
                    "totalRevenue": 2450000,
                    "monthlyGrowth": 12.5,
                    "activeCustomers": 89
                },
                "recentActivity": [
                    {
                        "type": "order_created",
                        "message": "New order ORD001 created by ABC Corporation",
                        "timestamp": "2024-01-16 10:30:00"
                    },
                    {
                        "type": "order_received",
                        "message": "Order ORD003 received from Tech Solutions Ltd",
                        "timestamp": "2024-01-16 09:15:00"
                    },
                    {
                        "type": "customer_added",
                        "message": "New customer Global Enterprises added",
                        "timestamp": "2024-01-16 08:45:00"
                    },
                    {
                        "type": "order_completed",
                        "message": "Order ORD005 completed successfully",
                        "timestamp": "2024-01-15 16:20:00"
                    }
                ]
            },
            "settings": {
                "users": [
                    {
                        "id": "USER001",
                        "name": "Admin User",
                        "email": "<EMAIL>",
                        "role": "Administrator",
                        "status": "Active"
                    }
                ],
                "packingGroups": [
                    {
                        "id": "PG001",
                        "name": "Standard Packing",
                        "description": "Standard packaging for regular items"
                    }
                ]
            }
        };

        console.log('Data loaded successfully:', this.data);
    }

    setupEventListeners() {
        // Modern tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const section = tab.getAttribute('data-section');
                this.showSection(section);

                // Update active state
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });

        // Global search
        const globalSearch = document.getElementById('globalSearch');
        if (globalSearch) {
            globalSearch.addEventListener('input', (e) => {
                this.handleGlobalSearch(e.target.value);
            });
        }

        // Filters
        const filters = ['territoryFilter', 'stateFilter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });

        // Pagination
        const prevPage = document.getElementById('prevPage');
        const nextPage = document.getElementById('nextPage');
        if (prevPage) prevPage.addEventListener('click', () => this.changePage(-1));
        if (nextPage) nextPage.addEventListener('click', () => this.changePage(1));

        // Select all checkbox
        const selectAll = document.getElementById('selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });

        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
            this.currentSection = sectionName;

            // Load section-specific data
            this.loadSectionData(sectionName);
        }
    }

    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.renderDashboard();
                break;
            case 'new-orders':
                this.renderNewOrders();
                break;
            case 'received-orders':
                this.renderReceivedOrders();
                break;
            case 'open-orders':
                this.renderOpenOrders();
                break;
            case 'closed-orders':
                this.renderClosedOrders();
                break;
            case 'customers':
                this.renderCustomers();
                break;
            default:
                console.log(`Loading ${sectionName} section`);
        }
    }

    renderDashboard() {
        if (!this.data?.dashboard?.stats) return;

        const stats = this.data.dashboard.stats;

        // Update stat cards
        this.updateElement('totalOrdersValue', stats.totalOrders || 0);
        this.updateElement('completedOrdersValue', stats.closedOrders || 0);
        this.updateElement('pendingOrdersValue', stats.openOrders || 0);
        this.updateElement('revenueValue', this.formatCurrency(stats.totalRevenue || 0));

        // Update sidebar badges
        this.updateElement('newOrdersCount', stats.newOrders || 0);
        this.updateElement('receivedOrdersCount', stats.receivedOrders || 0);
        this.updateElement('openOrdersCount', stats.openOrders || 0);
        this.updateElement('closedOrdersCount', stats.closedOrders || 0);

        // Render recent orders table
        this.renderRecentOrdersTable();

        // Render activity feed
        this.renderActivityFeed();

        // Update charts
        this.updateCharts();

        // Update quick stats bar
        this.updateQuickStats();
    }

    updateQuickStats() {
        if (!this.data?.dashboard?.stats) return;

        const stats = this.data.dashboard.stats;
        const todayOrders = Math.floor(Math.random() * 15) + 5; // Simulated today's orders
        const weekOrders = Math.floor(stats.totalOrders * 0.3);
        const monthRevenue = this.formatCurrency(stats.totalRevenue);
        const pendingActions = stats.newOrders + Math.floor(stats.openOrders * 0.1);

        this.updateElement('todayOrders', todayOrders);
        this.updateElement('weekOrders', weekOrders);
        this.updateElement('monthRevenue', monthRevenue);
        this.updateElement('pendingActions', pendingActions);
    }

    renderRecentOrdersTable() {
        const tableBody = document.getElementById('recentOrdersTable');
        if (!tableBody) return;

        let recentOrders = [];

        // Combine all orders and sort by date
        if (this.data?.orders) {
            const allOrders = [
                ...(this.data.orders.newOrders || []),
                ...(this.data.orders.receivedOrders || []),
                ...(this.data.orders.openOrders || []),
                ...(this.data.orders.closedOrders || [])
            ];

            recentOrders = allOrders
                .sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate))
                .slice(0, 5);
        }

        tableBody.innerHTML = recentOrders.map(order => `
            <tr>
                <td><strong>${order.id}</strong></td>
                <td>${order.customerName}</td>
                <td><span class="status-badge ${order.status.toLowerCase()}">${order.status}</span></td>
                <td>${this.formatCurrency(order.orderValue || 0)}</td>
                <td>${this.formatDate(order.createdDate)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="oms.viewOrder('${order.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="oms.editOrder('${order.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderActivityFeed() {
        const activityFeed = document.getElementById('activityFeed');
        if (!activityFeed || !this.data?.dashboard?.recentActivity) return;

        const activities = this.data.dashboard.recentActivity;

        activityFeed.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="fas ${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-message">${activity.message}</div>
                    <div class="activity-time">${this.formatDateTime(activity.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }

    renderNewOrders() {
        const tableBody = document.getElementById('newOrdersTable');
        if (!tableBody || !this.data?.orders?.newOrders) return;

        const orders = this.data.orders.newOrders;
        const paginatedOrders = this.paginateData(orders);

        tableBody.innerHTML = paginatedOrders.map(order => `
            <tr>
                <td><input type="checkbox" class="form-check-input order-checkbox" value="${order.id}"></td>
                <td>${order.gstNumber}</td>
                <td>${order.shipToPan}</td>
                <td>${order.emailId}</td>
                <td>${order.billToGst}</td>
                <td><strong>${order.customerName}</strong></td>
                <td>${order.operatingUnit}</td>
                <td>${order.contactPerson}</td>
                <td>${order.contactNumber}</td>
                <td>${order.territory}</td>
                <td>${order.state}</td>
                <td><span class="status-badge ${order.status.toLowerCase()}">${order.status}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="oms.approveOrder('${order.id}')" title="Approve">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="oms.viewOrder('${order.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="oms.editOrder('${order.id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="oms.rejectOrder('${order.id}')" title="Reject">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.updatePagination(orders.length);
    }

    renderReceivedOrders() {
        const tableBody = document.getElementById('receivedOrdersTable');
        if (!tableBody || !this.data?.orders?.receivedOrders) return;

        const orders = this.data.orders.receivedOrders;

        tableBody.innerHTML = orders.map(order => `
            <tr>
                <td><strong>${order.orderNumber}</strong></td>
                <td>${order.oracleOrderNumber}</td>
                <td>${this.formatDate(order.createdDate)}</td>
                <td><span class="status-badge ${order.status.toLowerCase()}">${order.status}</span></td>
                <td>${order.zone}</td>
                <td><strong>${order.customerName}</strong></td>
                <td>${order.operatingUnit}</td>
                <td>${order.contactPerson}</td>
                <td>${order.contactNumber}</td>
                <td>${order.billToAddress}</td>
                <td>${order.state}</td>
                <td>${order.territory}</td>
                <td>${order.emailId}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="oms.viewOrder('${order.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="oms.editOrder('${order.id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderOpenOrders() {
        const tableBody = document.getElementById('openOrdersTable');
        if (!tableBody || !this.data?.orders?.openOrders) return;

        const orders = this.data.orders.openOrders;

        tableBody.innerHTML = orders.map(order => `
            <tr>
                <td><strong>${order.orderNumber}</strong></td>
                <td><strong>${order.customerName}</strong></td>
                <td><span class="status-badge ${order.status.toLowerCase()}">${order.status}</span></td>
                <td>${this.formatCurrency(order.orderValue)}</td>
                <td>${this.formatDate(order.createdDate)}</td>
                <td>${order.territory}</td>
                <td>${order.state}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="oms.closeOrder('${order.id}')" title="Close">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="oms.viewOrder('${order.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="oms.editOrder('${order.id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderClosedOrders() {
        const tableBody = document.getElementById('closedOrdersTable');
        if (!tableBody || !this.data?.orders?.closedOrders) return;

        const orders = this.data.orders.closedOrders;

        tableBody.innerHTML = orders.map(order => `
            <tr>
                <td><strong>${order.orderNumber}</strong></td>
                <td><strong>${order.customerName}</strong></td>
                <td><span class="status-badge ${order.status.toLowerCase()}">${order.status}</span></td>
                <td>${this.formatCurrency(order.orderValue)}</td>
                <td>${this.formatDate(order.createdDate)}</td>
                <td>${this.formatDate(order.closedDate)}</td>
                <td>${order.territory}</td>
                <td>${order.state}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="oms.viewOrder('${order.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="oms.reopenOrder('${order.id}')" title="Reopen">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderCustomers() {
        const tableBody = document.getElementById('customersTable');
        if (!tableBody || !this.data?.customers) return;

        const customers = this.data.customers;

        tableBody.innerHTML = customers.map(customer => `
            <tr>
                <td><strong>${customer.id}</strong></td>
                <td><strong>${customer.name}</strong></td>
                <td>${customer.email}</td>
                <td>${customer.phone}</td>
                <td>${customer.address}</td>
                <td>${customer.gstNumber}</td>
                <td><span class="status-badge ${customer.status.toLowerCase()}">${customer.status}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="oms.viewCustomer('${customer.id}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="oms.editCustomer('${customer.id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="oms.deleteCustomer('${customer.id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Chart initialization and updates
    initializeCharts() {
        this.initOrderTrendsChart();
        this.initOrderStatusChart();
    }

    initOrderTrendsChart() {
        const ctx = document.getElementById('orderTrendsChart');
        if (!ctx) return;

        this.charts.orderTrends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'New Orders',
                    data: [12, 19, 8, 15, 22, 18, 25],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Completed Orders',
                    data: [8, 15, 12, 18, 16, 20, 22],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    x: {
                        grid: {
                            color: '#f3f4f6'
                        }
                    }
                }
            }
        });
    }

    initOrderStatusChart() {
        const ctx = document.getElementById('orderStatusChart');
        if (!ctx) return;

        this.charts.orderStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['New', 'Received', 'Open', 'Closed'],
                datasets: [{
                    data: [23, 45, 67, 21],
                    backgroundColor: [
                        '#2563eb',
                        '#10b981',
                        '#f59e0b',
                        '#6b7280'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }

    updateCharts() {
        if (this.data?.dashboard?.stats) {
            const stats = this.data.dashboard.stats;

            // Update order status chart
            if (this.charts.orderStatus) {
                this.charts.orderStatus.data.datasets[0].data = [
                    stats.newOrders,
                    stats.receivedOrders,
                    stats.openOrders,
                    stats.closedOrders
                ];
                this.charts.orderStatus.update();
            }
        }
    }

    // Utility methods
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    formatCurrency(amount) {
        if (amount >= 100000) {
            return `₹${(amount / 100000).toFixed(1)}L`;
        }
        return `₹${amount.toLocaleString('en-IN')}`;
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatDateTime(dateTimeString) {
        if (!dateTimeString) return '-';
        const date = new Date(dateTimeString);
        return date.toLocaleString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    getActivityIcon(type) {
        const icons = {
            'order_created': 'fa-plus-circle',
            'order_received': 'fa-inbox',
            'order_completed': 'fa-check-circle',
            'customer_added': 'fa-user-plus',
            'payment_received': 'fa-credit-card'
        };
        return icons[type] || 'fa-info-circle';
    }

    paginateData(data) {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return data.slice(startIndex, endIndex);
    }

    updatePagination(totalItems) {
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        const pageNumbers = document.getElementById('pageNumbers');
        const prevPage = document.getElementById('prevPage');
        const nextPage = document.getElementById('nextPage');

        if (pageNumbers) {
            let paginationHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}"
                            onclick="oms.goToPage(${i})">${i}</button>
                `;
            }
            pageNumbers.innerHTML = paginationHTML;
        }

        if (prevPage) {
            prevPage.disabled = this.currentPage === 1;
        }
        if (nextPage) {
            nextPage.disabled = this.currentPage === totalPages;
        }
    }

    changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1) {
            this.currentPage = newPage;
            this.loadSectionData(this.currentSection);
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadSectionData(this.currentSection);
    }

    // Search and filter methods
    handleGlobalSearch(query) {
        console.log('Global search:', query);
        // Implement global search functionality
    }

    applyFilters() {
        const territory = document.getElementById('territoryFilter')?.value;
        const state = document.getElementById('stateFilter')?.value;

        console.log('Applying filters:', { territory, state });
        // Implement filtering logic
        this.loadSectionData(this.currentSection);
    }

    clearFilters() {
        document.getElementById('territoryFilter').value = '';
        document.getElementById('stateFilter').value = '';
        document.getElementById('newOrdersSearch').value = '';
        this.applyFilters();
    }

    toggleSelectAll(checked) {
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    // Order actions
    viewOrder(orderId) {
        console.log('Viewing order:', orderId);
        this.showNotification('Order details will be displayed here', 'info');
    }

    editOrder(orderId) {
        console.log('Editing order:', orderId);
        this.showNotification('Order edit functionality coming soon', 'info');
    }

    approveOrder(orderId) {
        console.log('Approving order:', orderId);
        this.showNotification('Order approved successfully', 'success');
        // Update order status and refresh view
        this.loadSectionData(this.currentSection);
    }

    rejectOrder(orderId) {
        if (confirm('Are you sure you want to reject this order?')) {
            console.log('Rejecting order:', orderId);
            this.showNotification('Order rejected', 'warning');
            this.loadSectionData(this.currentSection);
        }
    }

    closeOrder(orderId) {
        console.log('Closing order:', orderId);
        this.showNotification('Order closed successfully', 'success');
        this.loadSectionData(this.currentSection);
    }

    reopenOrder(orderId) {
        console.log('Reopening order:', orderId);
        this.showNotification('Order reopened successfully', 'info');
        this.loadSectionData(this.currentSection);
    }

    // Customer actions
    viewCustomer(customerId) {
        console.log('Viewing customer:', customerId);
        this.showNotification('Customer details will be displayed here', 'info');
    }

    editCustomer(customerId) {
        console.log('Editing customer:', customerId);
        this.showNotification('Customer edit functionality coming soon', 'info');
    }

    deleteCustomer(customerId) {
        if (confirm('Are you sure you want to delete this customer?')) {
            console.log('Deleting customer:', customerId);
            this.showNotification('Customer deleted successfully', 'success');
            this.loadSectionData(this.currentSection);
        }
    }

    // Modal methods
    showNewOrderModal() {
        document.getElementById('newOrderModal').style.display = 'flex';
    }

    hideNewOrderModal() {
        document.getElementById('newOrderModal').style.display = 'none';
        document.getElementById('newOrderForm').reset();
    }

    createNewOrder() {
        const form = document.getElementById('newOrderForm');
        const formData = new FormData(form);
        const orderData = Object.fromEntries(formData);

        console.log('Creating new order:', orderData);

        // Add to data structure (in real app, this would be an API call)
        const newOrder = {
            id: 'ORD' + String(Date.now()).slice(-6),
            ...orderData,
            status: 'New',
            createdDate: new Date().toISOString().split('T')[0],
            orderValue: Math.floor(Math.random() * 200000) + 50000
        };

        if (!this.data.orders.newOrders) {
            this.data.orders.newOrders = [];
        }
        this.data.orders.newOrders.unshift(newOrder);

        this.hideNewOrderModal();
        this.showNotification('New order created successfully', 'success');

        // Refresh current view
        this.loadSectionData(this.currentSection);
    }

    // Notification system
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 100px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    showError(message) {
        this.showNotification(message, 'danger');
    }

    // User menu toggle
    toggleUserMenu() {
        console.log('User menu toggled');
        // Implement user menu dropdown
    }
}

// Global functions for HTML onclick handlers
function showNewOrderModal() {
    if (window.oms) {
        window.oms.showNewOrderModal();
    }
}

function hideNewOrderModal() {
    if (window.oms) {
        window.oms.hideNewOrderModal();
    }
}

function createNewOrder() {
    if (window.oms) {
        window.oms.createNewOrder();
    }
}

function clearFilters() {
    if (window.oms) {
        window.oms.clearFilters();
    }
}

function toggleUserMenu() {
    if (window.oms) {
        window.oms.toggleUserMenu();
    }
}

function toggleFabMenu() {
    const fabMenu = document.getElementById('fabMenu');
    const fabMain = document.querySelector('.fab-main');

    if (fabMenu && fabMain) {
        fabMenu.classList.toggle('show');
        fabMain.classList.toggle('active');
    }
}

function showNewCustomerModal() {
    console.log('New customer modal - coming soon');
    if (window.oms) {
        window.oms.showNotification('New customer functionality coming soon', 'info');
    }
}

function exportData() {
    console.log('Export data functionality');
    if (window.oms) {
        window.oms.showNotification('Export functionality coming soon', 'info');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.oms = new OrderManagementSystem();
});